//* PACKAGES
import React, {useState, useEffect, useRef} from 'react'
import { Link, router } from '@inertiajs/react';
import { toast } from 'react-toastify';
import axios from 'axios';

//* ICONS
import { MdMoreVert} from "react-icons/md";
import { FaEye } from 'react-icons/fa';

//* COMPONENTS
import BankTransferStatusComponent from '../BankTransfer/BankTransferStatusComponent';
import DropDownContainer from "@/Components/DropDownContainer";
import Checkbox from "@/Components/Checkbox";

//* PARTIALS
//...

//* STATE
//...

//* HOOKS 
import { usePermissions } from '@/Hooks/usePermissions';

//* UTILS
import useOutsideClick from "@/Util/useOutsideClick";
import setDefaultDateFormat from "../../Util/setDefaultDateFormat";

//* ENUMS
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function WireTransferItemComponent(
    {
        item,
        columns = [], 
        handleClickNote, 
    }
) 
{
    //! PACKAGE
    const ref = useRef();
    
    //! HOOKS
    const { hasPermission } = usePermissions();
    
    //! VARIABLES
    //...

    //! STATES
    const [show, setShow]                             = useState(false);


    //! USE EFFECTS
    //...

    //! FUNCTIONS
    useOutsideClick(ref, () => {
        setShow(false);
    });

    const showActionButton = () =>
    {
        let shouldShow = false; 

        if (item.bankTransferDeletedAt)
        {
            shouldShow = false; 
        }
        else if (item.bankTransferVerifiedAt)
        {
            shouldShow = false; 
        }
        else if (item.bankTransferReviewedAt && !item.bankTransferDeletedAt)
        {
            shouldShow = true; 
        }
        else if (!item.bankTransferReviewedAt && !item.bankTransferDeletedAt)
        {
            shouldShow = true;             
        }

        return shouldShow;
    };

    const rowActions = 
    [
        {
            label           : 'verify',
            hasAccess       : hasPermission('billing.wire.transfer.verify-edit'),
            shouldDisplay   : true,
            handleEventClick: () =>
            {
                router.get(route("billing.wire.transfer.verify-edit", { id: [item.bankTransferId] }));
            } 
        },
    ];


    console.log(columns.includes('domains')); 

    const permittedActions = rowActions.filter(rowAction => rowAction.hasAccess && rowAction.shouldDisplay);

    return (
        <tr className="hover:bg-gray-100">
            <td
                className={`
                    ${columns.includes('referenceNumber') == true ? '' : 'hidden'}
                    font-semibold
                `}
            >
                {item.bankTransferReferenceNumber}
            </td>
            <td
                className={`
                    ${columns.includes('client') == true ? '' : 'hidden'}
                `}
            >
                {item.userEmail}
            </td>
            <td
                className={`
                    ${columns.includes('accountName') == true ? '' : 'hidden'}
                `}
            >
                {item.bankTransferAccountName}
            </td>
            <td
                className={`
                    ${columns.includes('company') == true ? '' : 'hidden'}
                `}
            >
                {item.bankTransferCompany}
            </td>
            <td
                className={`
                    ${columns.includes('domains') == true ? '' : 'hidden'}
                `}
            >
                {
                    item.domains
                        ? 
                            <div
                                className="flex flex-col gap-1 text-xs text-primary"
                            >
                                {
                                    item.domains.map(
                                        (domain, domainIndex) => 
                                        {
                                            return (
                                                <span
                                                    key={domainIndex}
                                                >
                                                    {domain}
                                                </span>
                                            );
                                        }
                                    )
                                }
                            </div>
                        :
                        null
                }
            </td>
            <td
                className={`
                    ${columns.includes('grossAmount') == true ? '' : 'hidden'}
                `}
            >
                {parseFloat(item.bankTransferGrossAmount).toFixed(2)}
            </td>
            <td
                className={`
                    ${columns.includes('serviceFee') == true ? '' : 'hidden'}
                `}
            >
                {parseFloat(item.bankTransferServiceFee).toFixed(2)}
            </td>
            <td
                className={`
                    ${columns.includes('netAmount') == true ? '' : 'hidden'}
                `}
            >
                {parseFloat(item.bankTransferNetAmount).toFixed(2)}
            </td>
            <td
                className={`
                    font-semibold text-primary uppercase
                    ${columns.includes('purpose') == true ? '' : 'hidden'}
                `}
            >
                {item.purpose}
            </td>
            <td
                className={`
                    ${columns.includes('status') == true ? '' : 'hidden'}
                `}
            >
                <BankTransferStatusComponent
                    item={item}
                />
            </td>
            <td
                className={`
                    ${columns.includes('note') == true ? '' : 'hidden'}
                `}
            >
                <div
                    className='
                        flex gap-2 items-center 
                        text-slate-500
                        cursor-pointer hover:text-primary ease-linear duration-100
                    '
                    onClick={handleClickNote}
                >
                    <span>View</span>
                    <FaEye/>
                </div>
            </td>
            <td
                className={`
                    ${columns.includes('dateCreated') == true ? '' : 'hidden'}
                `}
            >
                {setDefaultDateFormat(item.bankTransferCreatedAt) + ' ' + new Date(item.bankTransferCreatedAt + 'Z').toLocaleTimeString()}
            </td>
            <td
                className={`
                    ${columns.includes('dateUpdated') == true ? '' : 'hidden'}
                `}
            >
                {setDefaultDateFormat(item.bankTransferUpdatedAt) + ' ' + new Date(item.bankTransferUpdatedAt + 'Z').toLocaleTimeString()}
            </td>
            <td
                className={`
                    ${columns.includes('actions') == true ? '' : 'hidden'}
                `}
            >
                {showActionButton() && <span ref={ref} className="relative">
                    <button
                        className="flex items-center"
                        onClick={() => setShow(!show)}
                    >
                        <MdMoreVert className="cursor-pointer text-2xl rounded-full hover:bg-gray-200" />
                    </button>
                    <DropDownContainer show={show}>
                        {
                            permittedActions.length == 0
                                ?
                                    <div
                                        className='text-sm font-medium rounded-md p-2 text-danger'
                                    >
                                        No Actions Permitted
                                    </div>
                                :
                                    permittedActions.map(
                                        (rowAction, rowActionIndex) =>
                                        {
                                            return (
                                                <button
                                                    key={rowActionIndex}
                                                    className="hover:bg-gray-100 px-5 py-1 w-full text-left capitalize"
                                                    onClick={rowAction.handleEventClick}
                                                >
                                                    {rowAction.label}
                                                </button>
                                            );
                                        }
                                    )
                        }
                    </DropDownContainer>
                </span>}
            </td>
        </tr>
    );
}