<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\RequestDelete\Jobs\DomainEppCancellation;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DomainDeleteDataQueryService
{
    public static function instance()
    {
        return new self;
    }

    public function getDomainInfo($domainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'registered_domains.id', '=', 'domain_cancellation_requests.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'users.id as userID',
                'users.email as userEmail',
                'users.first_name',
                'users.last_name',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name',
                'domain_cancellation_requests.support_note'
            ])
            ->where('domains.id', $domainId)
            ->whereNull('domain_cancellation_requests.deleted_at')
            ->first();
    }

    public function getExpiredApprovedRequests()
    {
        $cutoffTime = Carbon::now()->subHours(24);

        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'registered_domains.id', '=', 'domain_cancellation_requests.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'users.id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name',
                'domain_cancellation_requests.support_note'
            ])
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->whereNull('domain_cancellation_requests.deleted_at')
            ->where('domain_cancellation_requests.feedback_date', '<=', $cutoffTime)
            ->get();
    }

    public function isRecentlyRegisteredDomain(string $domainId): bool
    {
        $domain = DB::client()->table('domains')->where('id', $domainId)->first();

        if (!$domain) {
            return false;
        }

        $createdAt = Carbon::parse($domain->created_at);
        return $createdAt->diffInDays(now()) <= 5;
    }

    public function getRegisteredDomainId($domainId): int
    {
        return DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->value('id');
    }

    public function calculateRefundAndRenewalStatus($domainId, $requestedAt): array
    {
        $domain = DB::client()->table('domains')->where('id', $domainId)->first();
        $registeredDomain = DB::client()->table('registered_domains')->where('domain_id', $domainId)->first();

        if (!$domain || !$registeredDomain) {
            return ['is_refunded' => false, 'is_renewal' => false];
        }

        $createdAt = Carbon::parse($domain->created_at);
        $requestedAtCarbon = Carbon::parse($requestedAt);
        $clientRenewAt = $registeredDomain->client_renew_at ? Carbon::parse($registeredDomain->client_renew_at) : null;

        $isRefunded = $createdAt->diffInDays($requestedAtCarbon) <= 5;
        $isRenewal = $clientRenewAt ? $requestedAtCarbon->greaterThan($clientRenewAt) : false;

        return [
            'is_refunded' => $isRefunded,
            'is_renewal' => $isRenewal
        ];
    }

    public function getRequestDetailsForRenewal(int $registeredDomainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->select('requested_at')
            ->first();
    }

    public function updateDomainStatus($domainId, $status): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update(['status' => $status, 'updated_at' => now()]);
    }

    public function createDomainCancellationRequest(array $insertData)
    {
        return DB::client()->table('domain_cancellation_requests')->insert($insertData);
    }

    public function updateDomainCancellationRequest($registeredDomainId, array $updateData)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update($updateData);
    }

    public function insertUserNotification(array $data, string $title, string $message): void
    {
        DB::client()->table('notifications')->insert([
            'user_id' => $data['userId'],
            'title' => $title,
            'message' => $message,
            'redirect_url' => '/domain',
            'created_at' => now(),
            'updated_at' => now(),
            'importance' => 'important',
        ]);
    }

    public function dispatchDeletionJob(array $data): void
    {
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail'],
            0
        );
    }
}
