//* PACKAGES 
import React from "react";

//* ICONS
//.. 

//* COMPONENTS
//...

//* PARTIALS
//...

//* STATE
//...

//* UTILS 
//...

//* CONSTANTS
//...

//* CUSTOM HOOKS
//...

//* TYPES
//...

export default function AppTabGroupComponent(
    {
        //! VALUES
        items, 
        
        //! STATES
        activeTab
        
        //! EVENTS
        //...
    }
)
{
    return (
        <div
            className="flex gap-5 flex-col"
        >
            <div
                className="border-gray-100 border-b"
            >
                <div
                    className="flex flex-col md:flex-row"
                >
                    {
                        items.labels.map(
                            (item, index) => 
                            {
                                return (
                                    <div
                                        key={index}
                                        className={`
                                            hover:bg-slate-50 px-5 py-2 capitalize text-sm cursor-pointer ease-in duration-200 tracking-wide
                                            ${item.isActive ? 'bg-slate-100 font-bold text-primary' : 'font-semibold'}
                                        `}
                                        onClick={item.eventOnClick}
                                    >
                                        {item.value}
                                    </div>
                                );
                            }
                        )
                    }
                </div>
            </div>

            <div
                className=""
            >
                {
                    items.content.map(
                        (item, index) => 
                        {
                            return (
                                <div
                                    key={index}
                                    className={`
                                        ${item.isActive ? 'flex' : 'hidden'}
                                    `}
                                >
                                    {item.value}
                                </div>
                            );
                        }
                    )
                }
            </div>
        </div>
    );
}
