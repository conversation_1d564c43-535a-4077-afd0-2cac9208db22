<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Client\Constants\DomainStatus;
use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Requests\ShowListRequest;
use App\Traits\CursorPaginate;
use Carbon\Carbon;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\RequestDelete\Jobs\DomainEppCancellation;

class DatabaseQueryService
{
    use CursorPaginate;

    private static $pageLimit = 20;

    public static function instance()
    {
        $DatabaseQueryService = new self;

        return $DatabaseQueryService;
    }

    public function get(ShowListRequest $request)
    {
        $pageLimit = $request->input('limit', self::$pageLimit);
        $builder = self::baseQuery();
        self::whenHasDomain($builder, $request);
        self::whenHasEmail($builder, $request);
        self::whenHasStatusType($builder, $request);
        self::whenHas<PERSON><PERSON>rby($builder, $request);
        $builder = $builder->paginate($pageLimit)->withQueryString();
        return [
            ...CursorPaginate::cursor($builder, self::paramToURI($request)),
            "search" => $request->search ?? ""
        ];
    }
    public function supportNoteSave(ShowListRequest $request)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('id', $request->deletion_id)
            ->update([
                'support_note' => $request->support_note,
                'support_agent_name' => Auth::user()->name . ' (' . Auth::user()->email . ')',
                'feedback_date' => now(),
            ]);
        //return CursorPaginate::cursor($builder, self::paramToURI($request));
    }

    public function getDomainInfo($domainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'users.id as userID',
                'users.email as userEmail',
                'users.first_name',
                'users.last_name',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.requested_at'
            ])
            ->where('domains.id', $domainId)
            ->first();
    }

    public function getExpiredApprovedRequests()
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select([
                'domains.id as domainId',
                'domains.name as domainName',
                'domains.status as domainStatus',
                'users.id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason',
                'domain_cancellation_requests.feedback_date as approvedDate',
                'domain_cancellation_requests.support_agent_id',
                'domain_cancellation_requests.support_agent_name'
            ])
            ->where('domains.status', 'IN_PROCESS')
            ->whereNotNull('domain_cancellation_requests.support_agent_id')
            ->whereNotNull('domain_cancellation_requests.deleted_at')
            ->whereNotNull('domain_cancellation_requests.feedback_date')
            ->where('domain_cancellation_requests.feedback_date', '<=', now()->subHours(24))
            ->limit(500)
            ->get();
    }

    public function getRequestDetailsForRenewal($registeredDomainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->select('requested_at')
            ->first();
    }

    public function getRegisteredDomainId(string $domainId): int
    {
        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        if (!$registeredDomain) {
            throw new \Exception("Registered domain not found for domain ID: {$domainId}");
        }

        return $registeredDomain->id;
    }

    public function updateDomainStatus(string $domainId, string $status): void
    {
        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => $status,
                'updated_at' => now(),
            ]);
    }

    public function insertUserNotification(array $data, string $title, string $message): void
    {
        DB::client()->table('notifications')->insert([
            'user_id' => $data['userId'],
            'title' => $title,
            'message' => $message,
            'redirect_url' => '/domain',
            'created_at' => now(),
            'updated_at' => now(),
            'importance' => 'important',
        ]);
    }

    public function isRecentlyRegisteredDomain(string $domainId): bool
    {
        $domain = DB::client()->table('domains')->where('id', $domainId)->first();

        if (!$domain) {
            return false;
        }

        $createdAt = Carbon::parse($domain->created_at);
        return $createdAt->diffInDays(now()) <= 5;
    }

    public function dispatchDeletionJob(array $data): void
    {
        DomainEppCancellation::dispatch(
            $data['domainId'],
            $data['domainName'],
            $data['userId'],
            $data['userEmail'],
            $data['reason'] ?? 'Domain deletion request',
            $data['createdDate'] ?? now()->toDateTimeString(),
            $data['supportNote'],
            $data['adminId'],
            $data['adminName'],
            $data['adminEmail'],
            0
        );
    }

    public function updateDomainCancellationRequest($registeredDomainId, array $updateData)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->where('registered_domain_id', $registeredDomainId)
            ->update($updateData);
    }

    public function createDomainCancellationRequest(array $insertData)
    {
        return DB::client()->table('domain_cancellation_requests')->insert($insertData);
    }

    public function calculateRefundAndRenewalStatus($domainId, $requestedAt = null)
    {
        $requestedAt = $requestedAt ? Carbon::parse($requestedAt) : Carbon::now();

        $domain = DB::client()->table('domains')
            ->where('id', $domainId)
            ->select('created_at', 'client_renew_at', 'expiry')
            ->first();

        $domainCreatedAt = Carbon::parse($domain->created_at);
        $is_refunded = $domainCreatedAt->diffInDays($requestedAt) >= 5;

        $registeredDomain = DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->first();

        $hasRenewPayment = false;
        if ($registeredDomain) {
            $hasRenewPayment = DB::client()->table('payment_nodes')
                ->join('extension_fees', 'payment_nodes.extension_fee_id', '=', 'extension_fees.id')
                ->join('fees', 'extension_fees.fee_id', '=', 'fees.id')
                ->where('payment_nodes.registered_domain_id', $registeredDomain->id)
                ->where('fees.type', 'RENEW')
                ->where('payment_nodes.status', 'COMPLETED')
                ->exists();
        }

        if (!$hasRenewPayment) {
            $is_renewal = true;
        } else {
            $clientRenewAt = Carbon::parse($domain->client_renew_at);
            $is_renewal = $clientRenewAt->diffInDays($requestedAt) > 4;
        }

        return [
            'is_refunded' => $is_refunded,
            'is_renewal' => $is_renewal
        ];
    }

    // PRIVATE Functions

    private function baseQuery(): Builder
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('registered_domains', 'domain_cancellation_requests.registered_domain_id', '=', 'registered_domains.id')
            ->join('domains', 'registered_domains.domain_id', '=', 'domains.id')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->select(self::getSelectFields());
    }

    private function getSelectFields(): array
    {
        return [
            'domains.name as domainName',
            'domains.status',
            'domains.deleted_at as domainDeletedAt',
            'domains.created_at',
            'domains.id as domain_id',
            'domains.expiry',
            'users.id as user_id',
            'users.email',
            'users.first_name',
            'users.last_name',
            'domain_cancellation_requests.deleted_at',
            'domain_cancellation_requests.requested_at',
            'domain_cancellation_requests.reason',
            'domain_cancellation_requests.id as dcrID',
            'domain_cancellation_requests.support_agent_id',
            'domain_cancellation_requests.support_agent_name',
            'domain_cancellation_requests.support_note',
            'domain_cancellation_requests.feedback_date',
            'domain_cancellation_requests.registered_domain_id',
            'registered_domains.status as rstatus'
        ];
    }

    private function whenHasStatusType(Builder &$builder, ShowListRequest $request): void
    {
        if (!$request->has('statusType')) {
            return;
        }

        match ($request->statusType) {
            StatusTypes::ALL => null,
            StatusTypes::PENDING => $builder->whereNull('domain_cancellation_requests.deleted_at')
                ->whereNull('domain_cancellation_requests.feedback_date'),
            StatusTypes::APPROVED => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                ->whereNotNull('domain_cancellation_requests.feedback_date')
                ->where('domains.status', DomainStatus::IN_PROCESS),
            StatusTypes::REJECTED => $builder->whereNull('domain_cancellation_requests.deleted_at')
                ->whereNotNull('domain_cancellation_requests.feedback_date')
                ->where(function ($q) {
                        $q->where('domains.status', DomainStatus::ACTIVE)
                        ->orWhere('domains.status', DomainStatus::EXPIRED);
                    }),
            StatusTypes::CANCELED,"CANCELED" => $builder->whereNotNull('domain_cancellation_requests.deleted_at')
                ->whereNull('domain_cancellation_requests.feedback_date')
                ->where(function ($q) {
                        $q->where('domains.status', DomainStatus::ACTIVE)
                        ->orWhere('domains.status', DomainStatus::EXPIRED);
                    }),
            StatusTypes::DELETED => $builder->where('domains.status', DomainStatus::DELETED),
            default => $builder->whereNull('domain_cancellation_requests.deleted_at')
        };
    }

    private function whenHasOrderby(Builder &$builder, ShowListRequest $request): void
    {
        $builder->when($request->has('orderby'), function (Builder $query) use ($request) {
            $orderby = explode(':', $request->orderby);
            $orderby[1] = trim($orderby[1]);//remove white Spaces
            if (count($orderby) == 2 && in_array($orderby[1], ['Asc', 'Desc'])) {
                switch ($orderby[0]) {
                    case 'Domain':
                        $query->orderBy('domainName', $orderby[1]);
                        break;
                    case 'Date Requested':
                        $query->orderBy('domain_cancellation_requests.requested_at', $orderby[1]);
                        break;
                    default:
                        $query->orderBy('domain_cancellation_requests.id', 'desc');
                }
            } else {
                $query->orderBy('domain_cancellation_requests.id', 'desc');
            }
        })
            ->when(!$request->has('orderby'), function (Builder $query) {
                $query->orderBy('domain_cancellation_requests.id', 'desc');
            });
    }

    private static function whenHasDomain(&$builder, $request)
    {
        $builder->when(($request->has('domain') || $request->has('search')), function (Builder $query) use ($request) {
            $domain = $request->domain ?? $request->search;
            $query->where('name', 'ilike', $domain . '%');
        });
    }

    private static function whenHasEmail(&$builder, $request)
    {
        $builder->when($request->has('email'), function (Builder $query) use ($request) {
            $email = $request->email;
            $query->where('users.email', 'ilike', $email . '%');
        });
    }

    private function paramToURI(ShowListRequest $request): array
    {
        $param = [];

        if ($request->has('statusType')) {
            $param[] = 'statusType=' . $request->statusType;
        }

        if ($request->has('orderby')) {
            $param[] = 'orderby=' . $request->orderby;
        }

        return $param;
    }
}
